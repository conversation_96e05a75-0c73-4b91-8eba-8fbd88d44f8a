#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
释永信-业内独创视频号爆流
佛教风格的视频处理软件
基于 Codebase_Shiyongxin.py 的视频合成功能
"""

import sys
import os
import glob
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QLineEdit, QTextEdit, 
                             QProgressBar, QFileDialog, QMessageBox, QFrame, QGridLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QIcon

# 引用现有的处理器
from Codebase_Shiyongxin import WolverineProcessor

class BuddhistVideoProcessor(WolverineProcessor):
    """继承并修改原处理器，适配新的输出文件名"""
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, config_code="", delete_used_b=False):
        super().__init__(ffmpeg_path, a_video_list, b_video_list, output_dir, config_code, delete_used_b)
        print("[释永信] 佛教视频处理器初始化完成")
    
    def process_video_pair(self, a_video, b_video, pair_index):
        """重写处理方法，修改输出文件名为：A视频文件名_开光成功.mp4"""
        try:
            print(f"[释永信] 开始处理第{pair_index}个视频对")
            print(f"[释永信] A视频: {os.path.basename(a_video)}")
            print(f"[释永信] B视频: {os.path.basename(b_video)}")

            # 生成输出文件名：A视频文件名_开光成功.mp4
            a_name = Path(a_video).stem
            output_filename = f"{a_name}_开光成功.mp4"
            final_output = os.path.join(self.output_dir, output_filename)

            # 临时文件路径
            temp_video = os.path.join(self.temp_dir, f"temp_{pair_index}.mp4")
            temp_audio = os.path.join(self.temp_dir, f"temp_audio_{pair_index}.mp4")
            temp_dar = os.path.join(self.temp_dir, f"temp_dar_{pair_index}.mp4")

            # 调用父类的核心处理逻辑，但使用新的输出文件名
            return self._process_video_core(a_video, b_video, pair_index, final_output, 
                                          temp_video, temp_audio, temp_dar, output_filename)

        except Exception as e:
            print(f"[释永信] 处理视频对失败: {e}")
            return False

    def _process_video_core(self, a_video, b_video, pair_index, final_output, 
                           temp_video, temp_audio, temp_dar, output_filename):
        """核心视频处理逻辑（从父类复制并修改）"""
        import cv2
        import numpy as np
        import subprocess
        import shutil
        
        # ========= 获取 A 时长/帧数 =========
        cap_a = cv2.VideoCapture(a_video)
        if not cap_a.isOpened():
            print(f"[释永信] 无法打开A视频: {a_video}")
            return False

        fps_a = cap_a.get(cv2.CAP_PROP_FPS) or self.FPS
        total_frames = int(cap_a.get(cv2.CAP_PROP_FRAME_COUNT))
        frames_out = int((total_frames / fps_a) * self.FPS)

        cap_b = cv2.VideoCapture(b_video)
        if not cap_b.isOpened():
            print(f"[释永信] 无法打开B视频: {b_video}")
            cap_a.release()
            return False

        # ========= 写 OpenCV 合成视频 =========
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        writer = cv2.VideoWriter(temp_video, fourcc, self.FPS, self.SIZE_OUT)
        if not writer.isOpened():
            print("[释永信] VideoWriter 无法打开！")
            cap_a.release()
            cap_b.release()
            return False

        print(f"[释永信] 开始 独家视频号引擎 合成 {frames_out} 帧…")
        mask_cache = {}
        frame_idx = 0

        while frame_idx < frames_out and not self.is_cancelled:
            ret_b, frame_b = cap_b.read()
            if not ret_b:
                cap_b.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            frame_b = self.resize_and_crop(frame_b, self.SIZE_BG)

            ret_a, frame_a = cap_a.read()
            if not ret_a:
                break

            h_a, w_a = frame_a.shape[:2]
            scale = self.SIZE_BG / h_a
            new_w = int(w_a * scale)
            frame_a = cv2.resize(frame_a, (new_w, self.SIZE_BG), interpolation=cv2.INTER_LINEAR)

            if new_w not in mask_cache:
                mask_cache[new_w] = self.make_mask(self.SIZE_BG, new_w, self.FEATHER)
            mask = mask_cache[new_w]
            mask_f = mask / 255.0
            inv = 1 - mask_f

            x = (self.SIZE_BG - new_w)//2
            roi = frame_b[:, x:x+new_w]
            blended = (roi * inv[...,None] + frame_a * mask_f[...,None]).astype(np.uint8)
            frame_b[:, x:x+new_w] = blended

            stretched = cv2.resize(frame_b, self.SIZE_OUT, interpolation=cv2.INTER_LINEAR)
            writer.write(stretched)

            frame_idx += 1

            # 实时更新进度 - 每10帧更新一次
            if frame_idx % 10 == 0 or frame_idx == frames_out:
                # 计算OpenCV合成进度（占总进度的70%）
                opencv_progress = int((frame_idx / frames_out) * 70)
                self.progress_updated.emit(opencv_progress, f"开光合成中: {frame_idx}/{frames_out} 帧 ({opencv_progress}%)")

            # 每100帧打印一次日志
            if frame_idx % 100 == 0:
                print(f"[释永信] 已处理 {frame_idx}/{frames_out} 帧")

        cap_a.release()
        cap_b.release()
        writer.release()

        if self.is_cancelled:
            return False

        print("[释永信] temp视频画面合成完成")
        self.progress_updated.emit(70, "开光合成完成，开始音频混流...")

        # ========= FFmpeg 混流音频 + DAR =========
        cmd_mix = [
            self.ffmpeg_path, "-y",
            "-i", temp_video,
            "-i", a_video,
            "-map", "0:v", "-map", "1:a?",
            "-c:v", "copy", "-c:a", "aac",
            "-aspect", "1:1",
            temp_audio
        ]
        print("[释永信] 调用 FFmpeg 混流音轨…")
        self.progress_updated.emit(75, "音频混流中...")
        result = subprocess.run(cmd_mix, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"[释永信] FFmpeg混流失败: {result.stderr}")
            return False

        print("[释永信] 音频混流完成")
        self.progress_updated.emit(85, "音频混流完成，开始多轨封装...")

        # ========= 再执行一次极简 DAR 修正 =========
        cmd_dar = [
            self.ffmpeg_path, "-y",
            "-i", temp_audio,
            "-aspect", "1:1",
            temp_dar
        ]
        print("[释永信] 再次执行 DAR 修正命令…")
        self.progress_updated.emit(90, "DAR修正中...")
        result = subprocess.run(cmd_dar, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"[释永信] DAR修正失败: {result.stderr}")
            return False

        # 移动到最终输出位置
        shutil.move(temp_dar, final_output)
        print(f"[释永信] 最终输出: {output_filename}")
        self.progress_updated.emit(95, f"开光成功: {output_filename}")

        # 删除使用过的B视频（如果启用）
        if self.delete_used_b and b_video not in self.used_b_videos:
            try:
                os.remove(b_video)
                self.used_b_videos.add(b_video)
                print(f"[释永信] 已删除使用过的B视频: {os.path.basename(b_video)}")
                self.progress_updated.emit(98, "清理B视频完成")
            except Exception as e:
                print(f"[释永信] 删除B视频失败: {e}")

        self.progress_updated.emit(100, f"视频开光完成: {output_filename}")
        return True


class BuddhistVideoApp(QMainWindow):
    """释永信-业内独创视频号爆流 主界面"""
    
    def __init__(self):
        super().__init__()
        self.processor = None
        self.ffmpeg_path = "ffmpeg"  # 默认FFmpeg路径
        self.init_ui()
        
    def init_ui(self):
        """初始化佛教风格的用户界面"""
        self.setWindowTitle("释永信-业内独创视频号爆流 🙏")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置佛教风格的样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFF8DC, stop:1 #F5DEB3);
            }
            QLabel {
                color: #8B4513;
                font-family: "Microsoft YaHei", "SimHei";
                font-weight: bold;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #DAA520, stop:1 #B8860B);
                color: white;
                border: 2px solid #8B4513;
                border-radius: 8px;
                padding: 8px 16px;
                font-family: "Microsoft YaHei", "SimHei";
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #DAA520);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #B8860B, stop:1 #8B7355);
            }
            QLineEdit {
                border: 2px solid #CD853F;
                border-radius: 4px;
                padding: 4px;
                background: white;
                font-family: "Microsoft YaHei", "SimHei";
            }
            QTextEdit {
                border: 2px solid #CD853F;
                border-radius: 4px;
                background: white;
                font-family: "Consolas", "Microsoft YaHei";
            }
            QProgressBar {
                border: 2px solid #8B4513;
                border-radius: 4px;
                text-align: center;
                font-weight: bold;
                color: #8B4513;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #DAA520);
                border-radius: 2px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🙏 释永信-业内独创视频号爆流 🙏")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; color: #8B0000; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("南无阿弥陀佛 · 视频开光成功 · 功德无量")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("font-size: 14px; color: #8B4513; margin-bottom: 20px;")
        main_layout.addWidget(subtitle_label)
        
        # 文件选择区域
        self.create_file_selection_area(main_layout)
        
        # 控制按钮区域
        self.create_control_area(main_layout)
        
        # 进度显示区域
        self.create_progress_area(main_layout)
        
        # 日志显示区域
        self.create_log_area(main_layout)
        
    def create_file_selection_area(self, parent_layout):
        """创建文件选择区域"""
        # 文件选择框架
        file_frame = QFrame()
        file_frame.setFrameStyle(QFrame.Box)
        file_frame.setStyleSheet("QFrame { border: 2px solid #CD853F; border-radius: 8px; padding: 10px; }")
        parent_layout.addWidget(file_frame)
        
        file_layout = QGridLayout(file_frame)
        
        # A视频文件夹选择
        file_layout.addWidget(QLabel("🎬 A视频文件夹（主视频）:"), 0, 0)
        self.a_folder_edit = QLineEdit()
        self.a_folder_edit.setPlaceholderText("请选择包含主视频的文件夹...")
        file_layout.addWidget(self.a_folder_edit, 0, 1)
        
        a_browse_btn = QPushButton("📁 浏览")
        a_browse_btn.clicked.connect(self.browse_a_folder)
        file_layout.addWidget(a_browse_btn, 0, 2)
        
        # B视频文件夹选择
        file_layout.addWidget(QLabel("🎭 B视频文件夹（背景视频）:"), 1, 0)
        self.b_folder_edit = QLineEdit()
        self.b_folder_edit.setPlaceholderText("请选择包含背景视频的文件夹...")
        file_layout.addWidget(self.b_folder_edit, 1, 1)
        
        b_browse_btn = QPushButton("📁 浏览")
        b_browse_btn.clicked.connect(self.browse_b_folder)
        file_layout.addWidget(b_browse_btn, 1, 2)
        
        # 输出目录选择
        file_layout.addWidget(QLabel("💾 输出目录:"), 2, 0)
        self.output_folder_edit = QLineEdit()
        self.output_folder_edit.setPlaceholderText("默认为A视频所在文件夹...")
        file_layout.addWidget(self.output_folder_edit, 2, 1)
        
        output_browse_btn = QPushButton("📁 浏览")
        output_browse_btn.clicked.connect(self.browse_output_folder)
        file_layout.addWidget(output_browse_btn, 2, 2)
        
    def create_control_area(self, parent_layout):
        """创建控制按钮区域"""
        control_layout = QHBoxLayout()
        
        # 开始处理按钮
        self.start_btn = QPushButton("🙏 开始开光")
        self.start_btn.setStyleSheet("QPushButton { font-size: 16px; padding: 12px 24px; }")
        self.start_btn.clicked.connect(self.start_processing)
        control_layout.addWidget(self.start_btn)
        
        # 取消按钮
        self.cancel_btn = QPushButton("⏹ 停止开光")
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.clicked.connect(self.cancel_processing)
        control_layout.addWidget(self.cancel_btn)
        
        # FFmpeg路径设置
        control_layout.addWidget(QLabel("FFmpeg路径:"))
        self.ffmpeg_edit = QLineEdit(self.ffmpeg_path)
        self.ffmpeg_edit.setMaximumWidth(200)
        control_layout.addWidget(self.ffmpeg_edit)
        
        parent_layout.addLayout(control_layout)
        
    def create_progress_area(self, parent_layout):
        """创建进度显示区域"""
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        parent_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("准备就绪，等待开光...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 14px; color: #8B4513; margin: 5px;")
        parent_layout.addWidget(self.status_label)
        
    def create_log_area(self, parent_layout):
        """创建日志显示区域"""
        log_label = QLabel("📜 开光日志:")
        parent_layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setPlaceholderText("开光过程将在此显示...")
        parent_layout.addWidget(self.log_text)

    def browse_a_folder(self):
        """选择A视频文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择A视频文件夹（主视频）")
        if folder:
            self.a_folder_edit.setText(folder)
            # 如果输出目录为空，则设置为A视频所在文件夹
            if not self.output_folder_edit.text():
                self.output_folder_edit.setText(folder)
            self.log_message(f"已选择A视频文件夹: {folder}")

    def browse_b_folder(self):
        """选择B视频文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择B视频文件夹（背景视频）")
        if folder:
            self.b_folder_edit.setText(folder)
            self.log_message(f"已选择B视频文件夹: {folder}")

    def browse_output_folder(self):
        """选择输出目录"""
        folder = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if folder:
            self.output_folder_edit.setText(folder)
            self.log_message(f"已选择输出目录: {folder}")

    def get_video_files(self, folder):
        """获取文件夹中的视频文件"""
        if not folder or not os.path.exists(folder):
            return []

        video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.flv', '*.wmv', '*.m4v']
        video_files = []

        for ext in video_extensions:
            video_files.extend(glob.glob(os.path.join(folder, ext)))
            video_files.extend(glob.glob(os.path.join(folder, ext.upper())))

        return sorted(video_files)

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.append(f"[{self.get_current_time()}] {message}")
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def get_current_time(self):
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def start_processing(self):
        """开始视频处理"""
        # 验证输入
        a_folder = self.a_folder_edit.text().strip()
        b_folder = self.b_folder_edit.text().strip()
        output_folder = self.output_folder_edit.text().strip()

        if not a_folder:
            QMessageBox.warning(self, "警告", "请选择A视频文件夹！")
            return

        if not b_folder:
            QMessageBox.warning(self, "警告", "请选择B视频文件夹！")
            return

        # 如果输出目录为空，使用A视频所在文件夹
        if not output_folder:
            output_folder = a_folder
            self.output_folder_edit.setText(output_folder)

        # 获取视频文件列表
        a_videos = self.get_video_files(a_folder)
        b_videos = self.get_video_files(b_folder)

        if not a_videos:
            QMessageBox.warning(self, "警告", f"A视频文件夹中没有找到视频文件！\n路径: {a_folder}")
            return

        if not b_videos:
            QMessageBox.warning(self, "警告", f"B视频文件夹中没有找到视频文件！\n路径: {b_folder}")
            return

        # 创建输出目录
        os.makedirs(output_folder, exist_ok=True)

        # 更新FFmpeg路径
        self.ffmpeg_path = self.ffmpeg_edit.text().strip() or "ffmpeg"

        # 显示处理信息
        self.log_message("=" * 50)
        self.log_message("🙏 开始视频开光仪式")
        self.log_message(f"📁 A视频文件夹: {a_folder}")
        self.log_message(f"📁 B视频文件夹: {b_folder}")
        self.log_message(f"💾 输出目录: {output_folder}")
        self.log_message(f"🎬 找到 {len(a_videos)} 个A视频文件")
        self.log_message(f"🎭 找到 {len(b_videos)} 个B视频文件")
        self.log_message(f"⚙️ FFmpeg路径: {self.ffmpeg_path}")
        self.log_message("=" * 50)

        # 启动处理器
        self.processor = BuddhistVideoProcessor(
            ffmpeg_path=self.ffmpeg_path,
            a_video_list=a_videos,
            b_video_list=b_videos,
            output_dir=output_folder,
            config_code="",
            delete_used_b=False
        )

        # 连接信号
        self.processor.progress_updated.connect(self.update_progress)
        self.processor.process_finished.connect(self.process_finished)

        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备开光...")

        # 启动处理线程
        self.processor.start()

    def cancel_processing(self):
        """取消视频处理"""
        if self.processor:
            self.processor.cancel()
            self.log_message("⏹ 用户取消开光仪式")

    def update_progress(self, progress, message):
        """更新进度"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
        self.log_message(message)

    def process_finished(self, success, message):
        """处理完成"""
        self.progress_bar.setVisible(False)
        self.start_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)

        if success:
            self.status_label.setText("🎉 开光仪式圆满完成！")
            self.log_message("🎉 所有视频开光成功！功德无量！")
            QMessageBox.information(self, "开光成功", f"视频开光仪式圆满完成！\n\n{message}")
        else:
            self.status_label.setText("❌ 开光仪式中断")
            self.log_message(f"❌ 开光失败: {message}")
            QMessageBox.critical(self, "开光失败", f"开光仪式遇到问题：\n\n{message}")

        self.processor = None


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("释永信-业内独创视频号爆流")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("释永信工作室")

    # 创建主窗口
    window = BuddhistVideoApp()
    window.show()

    # 启动应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
