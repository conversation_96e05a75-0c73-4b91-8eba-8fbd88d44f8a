@echo off
chcp 65001 >nul
echo.
echo 🙏 释永信-业内独创视频号爆流 打包工具
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python
    pause
    exit /b 1
)

echo ✅ Python环境检测正常
echo.

REM 检查必要文件
if not exist "释永信_业内独创视频号爆流.py" (
    echo ❌ 缺少主程序文件: 释永信_业内独创视频号爆流.py
    pause
    exit /b 1
)

if not exist "Codebase_shiyongxin.py" (
    echo ❌ 缺少依赖文件: Codebase_shiyongxin.py
    pause
    exit /b 1
)

echo ✅ 必要文件检查完成
echo.

REM 安装PyInstaller
echo 🔧 正在安装PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ❌ PyInstaller安装失败
    pause
    exit /b 1
)

echo ✅ PyInstaller安装成功
echo.

REM 安装其他依赖
echo 🔧 正在安装其他依赖...
pip install opencv-python numpy requests PyQt5
echo.

REM 清理之前的打包文件
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "*.spec" del /q *.spec

echo 📿 开始打包释永信软件...
echo.

REM 执行打包命令
pyinstaller ^
    --onefile ^
    --windowed ^
    --name="释永信_业内独创视频号爆流" ^
    --add-data="Codebase_shiyongxin.py;." ^
    --hidden-import=cv2 ^
    --hidden-import=numpy ^
    --hidden-import=requests ^
    --hidden-import=PyQt5 ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtGui ^
    --hidden-import=PyQt5.QtWidgets ^
    --collect-all=cv2 ^
    --exclude-module=tkinter ^
    --exclude-module=matplotlib ^
    --exclude-module=PIL ^
    --clean ^
    "释永信_业内独创视频号爆流.py"

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！请检查错误信息
    pause
    exit /b 1
)

echo.
echo ✅ 打包成功！
echo 📁 输出文件位置: dist\释永信_业内独创视频号爆流.exe
echo.

REM 检查输出文件
if exist "dist\释永信_业内独创视频号爆流.exe" (
    echo 🎉 exe文件已生成，大小:
    dir "dist\释永信_业内独创视频号爆流.exe" | findstr "释永信"
    echo.
    echo 🚀 是否立即运行测试？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        start "" "dist\释永信_业内独创视频号爆流.exe"
    )
) else (
    echo ❌ exe文件未找到，打包可能失败
)

echo.
echo 🙏 南无阿弥陀佛，打包完成！
pause
